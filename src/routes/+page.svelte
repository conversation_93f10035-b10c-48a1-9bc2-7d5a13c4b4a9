<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { setLocale, getLocale } from '$lib/paraglide/runtime';
	import { m } from '$lib/paraglide/messages.js';
	import LanguageSidebar from '$lib/components/LanguageSidebar.svelte';
	import Popover from '$lib/components/Popover.svelte';

	const supportedLocales = ['en', 'fa', 'ar', 'tr', 'fr', 'es'];

	// Determine text direction based on locale
	$: currentLocale = getLocale();
	$: isRTL = ['ar', 'fa'].includes(currentLocale);
	$: direction = isRTL ? ('rtl' as const) : ('ltr' as const);

	onMount(() => {
		if (browser) {
			// Check URL for locale parameter
			const urlParams = new URLSearchParams(window.location.search);
			const urlLocale = urlParams.get('lang');

			if (urlLocale && supportedLocales.includes(urlLocale)) {
				// Use locale from URL
				setLocale(urlLocale as any);
			} else {
				// Get user's preferred language from browser
				const browserLang = navigator.language.split('-')[0];
				const detectedLocale = supportedLocales.includes(browserLang) ? browserLang : 'en';
				setLocale(detectedLocale as any);

				// Update URL with detected locale if not English
				if (detectedLocale !== 'en') {
					const newUrl = new URL(window.location.href);
					newUrl.searchParams.set('lang', detectedLocale);
					window.history.replaceState({}, '', newUrl.toString());
				}
			}
		}
	});
</script>

<svelte:head>
	<!-- Basic Meta Tags -->
	<title>{m.page_title()}</title>
	<meta name="description" content={m.meta_description()} />
	<meta name="keywords" content={m.meta_keywords()} />
	<meta name="author" content="Who is Mahdi" />
	<meta name="robots" content="index, follow" />
	<meta name="language" content={currentLocale} />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />

	<!-- Canonical URL -->
	<link rel="canonical" href="https://whoismahdi.com/" />

	<!-- Alternate Language Links -->
	<link rel="alternate" hreflang="en" href="https://whoismahdi.com/" />
	<link rel="alternate" hreflang="fa" href="https://whoismahdi.com/?lang=fa" />
	<link rel="alternate" hreflang="ar" href="https://whoismahdi.com/?lang=ar" />
	<link rel="alternate" hreflang="tr" href="https://whoismahdi.com/?lang=tr" />
	<link rel="alternate" hreflang="fr" href="https://whoismahdi.com/?lang=fr" />
	<link rel="alternate" hreflang="es" href="https://whoismahdi.com/?lang=es" />
	<link rel="alternate" hreflang="x-default" href="https://whoismahdi.com/" />

	<!-- Open Graph Meta Tags -->
	<meta property="og:title" content={`${m.page_title()} - ${m.page_subtitle()}`} />
	<meta property="og:description" content={m.og_description()} />
	<meta property="og:type" content="website" />
	<meta property="og:url" content={`https://whoismahdi.com/${currentLocale === 'en' ? '' : '?lang=' + currentLocale}`} />
	<meta property="og:image" content="https://whoismahdi.com/logo.png" />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content={m.page_title()} />
	<meta property="og:site_name" content="Who is Mahdi" />
	<meta property="og:locale" content={currentLocale === 'fa' ? 'fa_IR' : currentLocale === 'ar' ? 'ar_SA' : currentLocale === 'tr' ? 'tr_TR' : currentLocale === 'fr' ? 'fr_FR' : currentLocale === 'es' ? 'es_ES' : 'en_US'} />

	<!-- Twitter Card Meta Tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content={`${m.page_title()} - ${m.page_subtitle()}`} />
	<meta name="twitter:description" content={m.og_description()} />
	<meta name="twitter:image" content="https://whoismahdi.com/logo.png" />

	<!-- Telegram-specific Meta Tags -->
	<meta property="telegram:channel" content="@whoismahdi" />
	<meta name="telegram:card" content="summary_large_image" />

	<!-- Favicon and Icons -->
	<link rel="icon" type="image/x-icon" href="/favicon.ico" />
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />

	<!-- Additional SEO Meta Tags -->
	<meta name="theme-color" content="#059669" />
	<meta name="msapplication-TileColor" content="#059669" />
	<meta name="application-name" content="Who is Mahdi" />

	<!-- Structured Data (JSON-LD) -->
	<script type="application/ld+json">
		{JSON.stringify({
			"@context": "https://schema.org",
			"@type": "WebPage",
			"name": m.page_title(),
			"description": m.meta_description(),
			"url": `https://whoismahdi.com/${currentLocale === 'en' ? '' : currentLocale}`,
			"inLanguage": currentLocale,
			"isPartOf": {
				"@type": "WebSite",
				"name": "Who is Mahdi",
				"url": "https://whoismahdi.com"
			},
			"about": {
				"@type": "Person",
				"name": "Mahdi",
				"description": m.imam_description()
			}
		})}
	</script>
</svelte:head>

<!-- Language Sidebar -->
<LanguageSidebar />

<main
  class="flex flex-col items-center justify-center gap-8 bg-emerald-100 p-4 relative"
  class:rtl-text={ isRTL }
  class:persian={ currentLocale === 'fa' }
  class:arabic={ currentLocale === 'ar' }
  class:font-sans={ !['ar', 'fa'].includes(currentLocale) }
  dir={direction}
>
  <div class="overlay"></div>

  <header class="grid grid-cols-[auto_auto] gap-x-2 items-center title">
    <h1 class="text-7xl md:text-9xl font-black row-span-2">?</h1>
    <h1 class="text-4xl md:text-5xl font-black whitespace-nowrap uppercase">
      {m.page_title()}
    </h1>
    <h3 class="text-2xl md:text-3xl font-light">
      {m.page_subtitle()}
    </h3>
  </header>

  <article class="w-full max-w-[75ch] flex flex-col items-center">
    <h4 class="font-bold text-center">
      {m.justice_quote()}
    </h4>
    <h6 class="font-ultra text-sm text-center">
      {m.justice_quote_sub()}
    </h6>
  </article>

  <article class="w-full max-w-[75ch] flex flex-col gap-8 items-center">
    <p class="text-start font-light leading-relaxed">
      {m.main_description_part1()}<Popover content={m.imam_description()} position="top" />{m.main_description_part2()}<Popover content={m.appointed_hour_description()} position="top" />{m.main_description_part3()}
    </p>

    <p class="uppercase font-extrabold text-center">
      {m.promise_quote()}
    </p>
  </article>
</main>

<style>
  main {
    height: 100dvh;
    min-height: fit-content;
  }
  .rtl-text {
    text-align: right;
  }

  .persian {
    font-family: "farsi-body";

    .title {
      font-family: "farsi-title";
    }
  }

  .arabic {
    font-family: "arabic-body";

    .title {
      font-family: "arabic-title";
    }
  }

  .overlay {
    --direct: 180deg;
    --reverse: 0deg;

    pointer-events: none;
    position: fixed;
    top: 0;
    /* left: 0; */
    width: 100vw;
    height: 100dvh;
    background-image:
      /* Fine grain noise */
      url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.08'/%3E%3C/svg%3E"),
      /* Larger cellular texture */
      url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='cellularFilter'%3E%3CfeTurbulence type='turbulence' baseFrequency='0.3' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23cellularFilter)' opacity='0.06'/%3E%3C/svg%3E"),
      linear-gradient(var(--direct), rgba(0, 128, 0, 0.15) 0, rgba(0, 0, 0, 0) 50%),
      linear-gradient(var(--direct), rgba(0,   0, 0, 0.15) 0, rgba(0, 0, 0, 0) 15%),
      linear-gradient(var(--direct), rgba(128, 0, 0, 0.05) 0, rgba(0, 0, 0, 0) 5%),
      linear-gradient(var(--reverse), rgba(0, 128, 0, 0.15) 0, rgba(0, 0, 0, 0) 50%),
      linear-gradient(var(--reverse), rgba(0,   0, 0, 0.15) 0, rgba(0, 0, 0, 0) 15%),
      linear-gradient(var(--reverse), rgba(128, 0, 0, 0.05) 0, rgba(0, 0, 0, 0) 5%);
    background-blend-mode: multiply, overlay, normal, normal, normal, normal, normal, normal;

    /* Additional grunge effects */
    filter: contrast(1.1) brightness(0.95);
  }
</style>
