<script lang="ts">
	import { getLocale } from '$lib/paraglide/runtime';

	interface Props {
		content: string;
		position?: 'top' | 'bottom' | 'left' | 'right';
	}

	let { content, position = 'top' }: Props = $props();
	
	let isVisible = $state(false);
	let popoverElement: HTMLDivElement;
	let triggerElement: HTMLButtonElement;
	
	$: currentLocale = getLocale();
	$: isRTL = currentLocale === 'ar' || currentLocale === 'fa';

	function showPopover() {
		isVisible = true;
	}

	function hidePopover() {
		isVisible = false;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			hidePopover();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

<div class="relative inline-block">
	<!-- Help Icon Trigger -->
	<button
		bind:this={triggerElement}
		class="inline-flex items-center justify-center w-4 h-4 ml-1 text-xs bg-emerald-600 text-white rounded-full hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-1 transition-colors duration-200"
		onmouseenter={showPopover}
		onmouseleave={hidePopover}
		onfocus={showPopover}
		onblur={hidePopover}
		aria-label="Help information"
		aria-describedby="popover-content"
	>
		?
	</button>

	<!-- Popover Content -->
	{#if isVisible}
		<div
			bind:this={popoverElement}
			id="popover-content"
			class="absolute z-50 px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg shadow-lg max-w-xs {position === 'top'
				? 'bottom-full mb-2 left-1/2 transform -translate-x-1/2'
				: position === 'bottom'
					? 'top-full mt-2 left-1/2 transform -translate-x-1/2'
					: position === 'left'
						? 'right-full mr-2 top-1/2 transform -translate-y-1/2'
						: 'left-full ml-2 top-1/2 transform -translate-y-1/2'}"
			class:rtl-popover={isRTL}
			role="tooltip"
		>
			<!-- Arrow -->
			<div
				class="absolute w-2 h-2 bg-white border-gray-200 transform rotate-45 {position === 'top'
					? 'top-full left-1/2 -translate-x-1/2 -translate-y-1/2 border-b border-r'
					: position === 'bottom'
						? 'bottom-full left-1/2 -translate-x-1/2 translate-y-1/2 border-t border-l'
						: position === 'left'
							? 'left-full top-1/2 -translate-x-1/2 -translate-y-1/2 border-t border-r'
							: 'right-full top-1/2 translate-x-1/2 -translate-y-1/2 border-b border-l'}"
			></div>
			
			<!-- Content -->
			<div class="relative z-10 text-gray-800 leading-relaxed" class:text-right={isRTL}>
				{content}
			</div>
		</div>
	{/if}
</div>

<style>
	.rtl-popover {
		font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
		direction: rtl;
	}
</style>
