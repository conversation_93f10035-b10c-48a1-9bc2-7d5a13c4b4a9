<script lang="ts">
  import { m } from '$lib/paraglide/messages.js';
  import { getLocale } from '$lib/paraglide/runtime';
  import LanguageSidebar from '$lib/components/LanguageSidebar.svelte';

  // Determine text direction based on locale
  $: currentLocale = getLocale();
  $: isRTL = ['ar', 'fa'].includes(currentLocale);
  $: direction = isRTL ? ('rtl' as const) : ('ltr' as const);
</script>

<svelte:head>
<title>{m.page_title()}</title>
</svelte:head>

<!-- Language Sidebar -->
<LanguageSidebar />

<main
  class="h-dvh flex flex-col items-center justify-center gap-8 bg-emerald-100 p-4"
  class:rtl-text={ isRTL }
  class:persian={ currentLocale === 'fa' }
  class:arabic={ currentLocale === 'ar' }
  class:font-sans={ !['ar', 'fa'].includes(currentLocale) }
  dir={direction}
>
  <header class="grid grid-cols-[auto_auto] gap-x-2 items-center title">
    <h1 class="text-7xl md:text-9xl font-black row-span-2">?</h1>
    <h1 class="text-4xl md:text-5xl font-black whitespace-nowrap uppercase">
      {m.page_title()}
    </h1>
    <h3 class="text-2xl md:text-3xl font-light">
      {m.page_subtitle()}
    </h3>
  </header>

  <article class="w-full max-w-[75ch] flex flex-col items-center">
    <h4 class="font-bold text-center">
      {m.justice_quote()}
    </h4>
    <h6 class="font-ultra text-sm text-center">
      {m.justice_quote_sub()}
    </h6>
  </article>

  <article class="w-full max-w-[75ch] flex flex-col gap-8 items-center">
    <p class="font-light text-center leading-relaxed">
      {m.main_description()}
    </p>

    <p class="uppercase font-extrabold text-center">
      {m.promise_quote()}
    </p>
  </article>
</main>

<style>
  .rtl-text {
    text-align: right;
  }

  .persian {
    font-family: "farsi-body";

    .title {
      font-family: "farsi-title";
    }
  }

  .arabic {
    font-family: "arabic-body";

    .title {
      font-family: "arabic-title";
    }
  }
</style>
