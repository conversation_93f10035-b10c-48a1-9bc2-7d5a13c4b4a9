services:
  db:
    image: postgres
    restart: always
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: mysecretpassword
      POSTGRES_DB: local
    volumes:
      - pgdata:/var/lib/postgresql/data

  whoismahdi_sveltekit:
    image: node:24.2.0
    restart: unless-stopped
    container_name: whoismahdi_sveltekit
    ports:
      - "3001:3000"
    volumes:
      - .:/home/<USER>
    command: bash -c "cd /home/<USER>/index.js"
    working_dir: /home/<USER>
volumes:
  pgdata:
