<script lang="ts">
	import { getLocale } from '$lib/paraglide/runtime';
	import { fly } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	let { children } = $props();
	
	// Track the current locale to trigger transitions
	let currentLocale = $derived(getLocale());
</script>

<!-- Content wrapper with transition effects -->
{#key currentLocale}
	<div
		in:fly={{ y: 30, duration: 600, delay: 300, easing: quintOut }}
		out:fly={{ y: -30, duration: 300, easing: quintOut }}
		class="transition-content"
	>
		{@render children()}
	</div>
{/key}

<style>
	.transition-content {
		will-change: transform, opacity;
	}
</style>
